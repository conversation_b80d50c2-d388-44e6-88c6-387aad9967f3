#This file is for functions that will parse a single room type
import json
from helpers.LLM_Handler import LLM_Handler, openAIModels, geminiModels
from helpers.Document_Processing import Doc_Processor


class room_type_parser:

    def __init__(self, doc, property_name, room_type, model="gpt-4o-mini",):
        """
        Initialize the document processor with the specified model.

        Args:
            model (str): The model to use for processing. Default is "gpt-4o-mini".
        """
        if not (model in openAIModels or model in geminiModels):
            raise Exception(f"Error - unknown model: {model}")
        self.llm_handler = LLM_Handler(model)
        self.document_processor = Doc_Processor()
        self.set_parameters(doc,property_name, room_type)
        
    def set_parameters(self, doc, property_name, room_type):
        """
        Set the document, property name, and room type for the parser.

        Args:
            doc (str): The document to check.
            property_name (str): The property name.
            room_type (str): The room type.
        """
        self.doc = doc
        self.property_name = property_name
        self.room_type = room_type

    def get_meal_type(self, types):
        """
        Get the meal type available in a specific room type of a property.

        Args:
            types (list): List of meal types.

        Returns:
            str: The meal type.
        """
        prompt = f"""In the following document which should contain accommodation information, what meal basis is available in the property "{self.property_name}" and the room type "{self.room_type}"?
        
        Remember!!:
        - A communal kitchen, shared kitchen, or kitchenette typically indicates a self-catering or self-service arrangement.

        Return **ONLY** one of the following meal basis, with no other text: {types}"""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        return response.strip()

    #RATES:
    #period?
    def get_room_capacity(self, sto=-1, num_attempts=3):
        """
        Get the capacity details for a specific room type during a specific period.

        Args:
            period (tuple): The period name and date range.
            sto (int, optional): The STO value. Defaults to -1.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            dict: The room capacity information.
        """
        rateterm = "published" if sto == 0 else "STO" if sto == 1 else "base"
        prompt = f"""In the following document which should contain accommodation information, what is the maximum capacity of a "{self.room_type}" at "{self.property_name}".
        Return a YAML response with the following data:
        BASE_CAPACITY: the base capacity of the room, for instance 2 for two people.
        TOTAL_CAPACITY: the total capacity of the room, for instance 6. This would include adults as well as children.
        MAX_ADULTS: The maximum number of adults that may stay at the accommodation. 
        MAX_CHILDREN: The maximum number of children that may stay at the accommodation.

        Remember to only return data relating to "{self.room_type}" at "{self.property_name}" and not any other potentially similar accommodations at different properties. If you cannot find figures related to this specific location, rather return zeros."""
        return self.llm_handler.sendMessageToLLM_Expecting_Yaml_Response(self.doc, prompt, num_attempts=num_attempts)
    
    def get_json_rate_formats(self, start_date, end_date, rate_type=None):
        if rate_type==None:
            rate_type="Normal"
        json_rate_format = {
            "room_rate_package": {
                "room_type":        { "type": "string",      "desc": f"Identifier or name of the room ({self.room_type})" },
                "is_available":     { "type": "boolean",     "desc": "Does this configuration even exist for the given dates & pax?" },
                "currency":         { "type": "string",      "desc": "ISO 4217 code (should be ZAR)" },
                "rate_amount":      { "type": "number",      "desc": "Numeric rate" },
                "rate_unit":        { "type": "string",      "enum": ["room_per_night", "person_per_night"], "desc": "What the rate_amount covers" },
                "date_from":        { "type": "string",      "format": "date",      "desc": f"First valid date, should be {start_date}" },
                "date_to":          { "type": "string",      "format": "date",      "desc": f"last valid date, should be {end_date}" },
                "base_capacity":    { "type": "integer",     "desc": "Number of guests included in base rate" },
                "total_capacity":   { "type": "integer",     "desc": "Max guests allowed in this unit" },
                "rate_type":        {"type": "string",       "desc": f"Rate type, eg 'Weekend' or 'Long Stay'. Should be '{rate_type}'"},
                "min_stay_nights":  { "type": "integer",     "desc": "Minimum length-of-stay in nights, if specified" },
                "additional_fee_rules": {
                "type": "array",
                "items": {
                    "name":         { "type": "string",  "desc": "E.g. 'Adult supplement'" },
                    "fee_amount":   { "type": "number",  "desc": "Numeric supplement amount" },
                    "fee_unit":     { "type": "string",  "enum": ["per_person_per_night"], "desc": "What this amount covers" },
                    "apply_after":  { "type": "integer", "desc": "Number of guests (beyond base_capacity) before this fee applies" },
                    "max_applicable":{ "type": "integer","desc": "Ceiling on how many extra pax this rule covers" }
                },
                "desc": "Zero-length if no extra fees"
                }
            }
        }

        #As an example, if the room has a base guest amount of two, with a base rate of R1500, and a cost of R300 per additional guest, and has 1 double, 2 single, 2 bench beds, that room can have up to 6 people. 
        json_rate_format_example = {
            "room_rate_package": {
                "room_type":         self.room_type,
                "is_available":      True,
                "currency":          "ZAR",
                "rate_amount":       1500,
                "rate_unit":         "room_per_night",
                "date_from":         "2025-08-01",
                "date_to":           "2025-08-31",
                "base_capacity":     2,
                "total_capacity":    4,
                "rate_type":         rate_type,
                "min_stay_nights":   0,
                "additional_fee_rules": [
                {
                    "name":         "Adult supplement",
                    "fee_amount":   300,
                    "fee_unit":     "per_person_per_night",
                    "apply_after":  2,
                    "max_applicable": 2
                }
                ]
            }
        }
        return json.dumps(json_rate_format, indent=2), json.dumps(json_rate_format_example, indent=2)
    
    def get_single_room_rate(self, period, sto=-1, num_attempts=3, rate_type=None):
        """
        Get the rate for hiring a room for one adult during a specific period.

        Args:
            period (tuple): The period name and date range.
            sto (int, optional): The STO value. Defaults to -1.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            dict: The room rate information.
        """
        json_rate_format, json_rate_format_example = self.get_json_rate_formats(period[1], period[2], rate_type)
        rateterm = "published" if sto==0 else "STO" if sto==1 else "base"
        rate_term = rate_type.strip() + " " + rate_term if isinstance(rate_type, str) and rate_type.strip() else rateterm
        additional_context = "Note that if there is a distinction between a published/rack rate and another rate (STO/NETT rate), return the STO rate." if sto!=0 else ""
        prompt = f"""In the following document which should contain accommodation information, what is the {rateterm} rate for hiring a  "{self.room_type}" at "{self.property_name}" for ONE adult during the "{period[0]} ({period[1]} - {period[2]})" period?
        Note that the document will likely be using the South African Rand currency. 
        {additional_context}
        Return a JSON response with the following data:
        
        {json_rate_format}

        Example:

        {json_rate_format_example}
        
        Remember to only return data relating to "{self.room_type}" at "{self.property_name}", and not any other potentially similar accommodations at different properties. If you cannot find figures related to this specific location, rather return zeros."""
        return self.llm_handler.sendMessageToLLM_Expecting_JSON_Response(self.doc,prompt,num_attempts=num_attempts)
       
    def get_double_room_rate(self, period, sto=-1, num_attempts=3, rate_type=None):
        """
        Get the rate for hiring a room for two adults during a specific period.

        Args:
            period (tuple): The period name and date range.
            sto (int, optional): The STO value. Defaults to -1.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            dict: The room rate information.
        """
        json_rate_format, json_rate_format_example = self.get_json_rate_formats(period[1], period[2], rate_type)
        rateterm = "published" if sto==0 else "STO" if sto==1 else "base"
        rate_term = rate_type.strip() + " " + rate_term if isinstance(rate_type, str) and rate_type.strip() else rateterm
        additional_context = "Note that if there is a distinction between a published/rack rate and another rate (STO/NETT rate), return the STO rate." if sto!=0 else ""
        prompt = f"""In the following document which should contain accommodation information, what is the {rateterm} rate for hiring a  "{self.room_type}" at "{self.property_name}" for TWO adults during the "{period[0]} ({period[1]} - {period[2]})" period?
        Note that the document will likely be using the South African Rand currency. Do not include any currency symbol in your answer.
        {additional_context}
        Return a JSON response with the following data:
        
        {json_rate_format}

        Remember to only return data relating to "{self.room_type}" at "{self.property_name}"during "{period[0]}", and not any other potentially similar accommodations at different properties. If you cannot find figures related to this specific location, rather return zeros.
        
        As an example, if the room has a base guest amount of two, with a base rate of R1500, and a cost of R300 per additional guest, and has 1 double, 2 single, 2 bench beds, that room can have up to 6 people. 
        {json_rate_format_example}
        """
        return self.llm_handler.sendMessageToLLM_Expecting_JSON_Response(self.doc,prompt,num_attempts=num_attempts)

    def get_triple_room_rate(self, period, sto=-1, num_attempts=3, rate_type=None):
        """
        Get the rate for hiring a room for three adults during a specific period.

        Args:
            period (tuple): The period name and date range.
            sto (int, optional): The STO value. Defaults to -1.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            dict: The room rate information.
        """
        json_rate_format, json_rate_format_example = self.get_json_rate_formats(period[1], period[2], rate_type)

        rateterm = "published" if sto==0 else "STO" if sto==1 else "base"
        rate_term = rate_type.strip() + " " + rate_term if isinstance(rate_type, str) and rate_type.strip() else rateterm
        additional_context = "Note that if there is a distinction between a published/rack rate and another rate (STO/NETT rate), return the STO rate." if sto!=0 else ""
        prompt = f"""In the following document which should contain accommodation information, what is the {rateterm} rate for hiring a  "{self.room_type}" at "{self.property_name}" for THREE adults during the "{period[0]} ({period[1]} - {period[2]})" period?
        Note that the document will likely be using the South African Rand currency. Do not include any currency symbol in your answer.
        {additional_context}
        Return a JSON response with the following data:

        {json_rate_format}

        Remember to only return data relating to "{self.room_type}" at "{self.property_name}" during "{period[0]}", and not any other potentially similar accommodations at different properties. If you cannot find figures related to this specific location, rather return zeros.

        As an example, if the room has a base guest amount of two, with a base rate of R1500, and a cost of R300 per additional guest, and has 1 double, 2 single, 2 bench beds, that room can have up to 6 people. 
        
        {json_rate_format_example}"""
        return self.llm_handler.sendMessageToLLM_Expecting_JSON_Response(self.doc,prompt,num_attempts=num_attempts)
        
    def get_quad_room_rate(self, period, sto=-1, num_attempts=3, rate_type=None):
        """
        Get the rate for hiring a room for four adults during a specific period.

        Args:
            period (tuple): The period name and date range.
            sto (int, optional): The STO value. Defaults to -1.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            dict: The room rate information.
        """
        json_rate_format, json_rate_format_example = self.get_json_rate_formats(period[1], period[2], rate_type)

        rateterm = "published" if sto==0 else "STO" if sto==1 else "base"
        rate_term = rate_type.strip() + " " + rate_term if isinstance(rate_type, str) and rate_type.strip() else rateterm
        additional_context = "Note that if there is a distinction between a published/rack rate and another rate (STO/NETT rate), return the STO rate." if sto!=0 else ""
        prompt = f"""In the following document which should contain accommodation information, what is the {rateterm} rate for hiring a  "{self.room_type}" at "{self.property_name}" for FOUR adults during the "{period[0]} ({period[1]} - {period[2]})" period?
        Note that the document will likely be using the South African Rand currency. Do not include any currency symbol in your answer.
        {additional_context}
        Return a JSON response with the following data:
        
        {json_rate_format}

        Remember to only return data relating to "{self.room_type}" at "{self.property_name}"during "{period[0]}", and not any other potentially similar accommodations at different properties. If you cannot find figures related to this specific location, rather return zeros.

        As an example, if the room has a base guest amount of two, with a base rate of R1500, and a cost of R300 per additional guest, and has 1 double, 2 single, 2 bench beds, that room can have up to 6 people. 
       
        {json_rate_format_example}"""
        return self.llm_handler.sendMessageToLLM_Expecting_JSON_Response(self.doc,prompt,num_attempts=num_attempts)

    def get_child_rate(self, period, sto=-1, num_attempts=3, child_info="", rate_type=None):
        """
        Get the additional cost for bringing a child to a room during a specific period.

        Args:
            period (tuple): The period name and date range.
            sto (int, optional): The STO value. Defaults to -1.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            dict: The additional cost information.
        """
        rateterm = "published " if sto==0 else "STO " if sto==1 else ""
        rate_term = rate_type.strip() + " " + rate_term if isinstance(rate_type, str) and rate_type.strip() else rateterm
        additional_context = "Note that if there is a distinction between a published/rack rate and another rate (STO/NETT rate), return the STO rate." if sto!=0 else ""
        prompt = f"""In the following document which should contain accommodation information, what is the {rateterm}additional cost for bringing a {child_info}child to a "{self.room_type}" at "{self.property_name}" during the "{period[0]} ({period[1]} - {period[2]})" period?
        Note that the document will likely be using the South African Rand currency. Do not include any currency symbol in your answer.
        {additional_context}
        Return only the fee that must be paid for bringing a child. If none is specified, this may be the same as the fee for an additional adult. If no additional fees can be found at all for this room, return 0. If a child is explicitly not allowed, return -1.
        Remember to only return data relating to "{self.room_type}" at "{self.property_name}"during "{period[0]}", and not any other potentially similar accommodations at different properties. If you cannot find figures related to this specific location, rather return zero.
        Return a YAML response with the following data:
        CHILD_RATE: value
        """
        for _ in range(num_attempts):
            try:
                # Attempt to get a response from the LLM
                response = self.llm_handler.sendMessageToLLM_Expecting_Yaml_Response(self.doc,prompt,num_attempts=num_attempts)
                if response.get("CHILD_RATE"):
                    return response["CHILD_RATE"]
            except Exception as e:
                print(e)
        return -1

    def get_child_2_rate(self, period, sto=-1, num_attempts=3):
        """
        Get the additional cost for bringing a second child to a room during a specific period.

        Args:
            period (tuple): The period name and date range.
            sto (int, optional): The STO value. Defaults to -1.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            dict: The additional cost information.
        """
        rateterm = "published " if sto==0 else "STO " if sto==1 else ""
        additional_context = "Note that if there is a distinction between a published/rack rate and another rate (STO/NETT rate), return the STO rate." if sto!=0 else ""
        prompt = f"""In the following document which should contain accommodation information, what is the {rateterm}additional cost for bringing a second child to a "{self.room_type}" at "{self.property_name}" during the "{period[0]} ({period[1]} - {period[2]})" period?
        Note that the document will likely be using the South African Rand currency. Do not include any currency symbol in your answer.
        {additional_context}
        Return only the fee that must be paid for bringing a child. If none is specified, this may be the same as the fee for an additional adult. If no additional fees can be found at all for this room, return 0. If a second child is explicitly not allowed, return -1.
        Remember to only return data relating to "{self.room_type}" at "{self.property_name}"during "{period[0]}", and not any other potentially similar accommodations at different properties. If you cannot find figures related to this specific location, rather return zero.
        Return a YAML response with the following data:
        CHILD_RATE: value
        """
        for _ in range(num_attempts):
            try:
                # Attempt to get a response from the LLM
                response = self.llm_handler.sendMessageToLLM_Expecting_Yaml_Response(self.doc,prompt,num_attempts=num_attempts)
                if response.get("CHILD_RATE"):
                    return response["CHILD_RATE"]
            except Exception as e:
                print(e)
        return -1

    def get_infant_rate(self, period, sto=-1, num_attempts=3):
        """
        Get the additional cost for bringing an infant to a room during a specific period.

        Args:
            period (tuple): The period name and date range.
            sto (int, optional): The STO value. Defaults to -1.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            dict: The additional cost information.
        """
        rateterm = "published " if sto==0 else "STO " if sto==1 else ""
        additional_context = "Note that if there is a distinction between a published/rack rate and another rate (STO/NETT rate), return the STO rate." if sto!=0 else ""
        prompt = f"""In the following document which should contain accommodation information, what is the {rateterm}additional cost for bringing an infant to a "{self.room_type}" at "{self.property_name}" during the "{period[0]} ({period[1]} - {period[2]})" period?
        Note that the document will likely be using the South African Rand currency. Do not include any currency symbol in your answer.
        {additional_context}
        Return only the fee that must be paid for bringing an infant. If this specific fee is not discussed anywhere for this room, return 0. If infants are explicitly not allowed, return -1.
        Remember to only return data relating to "{self.room_type}" at "{self.property_name}"during "{period[0]}", and not any other potentially similar accommodations at different properties. If you cannot find figures related to this specific location, rather return zero.
        Return a YAML response with the following data:
        INFANT_RATE: value
        """
        for _ in range(num_attempts):
            try:
                # Attempt to get a response from the LLM
                response = self.llm_handler.sendMessageToLLM_Expecting_Yaml_Response(self.doc,prompt,num_attempts=num_attempts)
                if response.get("INFANT_RATE"):
                    return response["INFANT_RATE"]
            except Exception as e:
                print(e)
        return -1
    
    def min_night_stay(self, num_attempts=3, attempt_num=0):
        prompt = f"""You are a travel agent, helping me make sense of some hotel documentation.
        Based on the following document, is there a minimum nights stay at a '{self.room_type}' at '{self.property_name}'? If yes, return only the number - eg 2. If not, return only 0. If the rule is more complex - for instance, the minimum night stay varies depending on on some other factors - return -1."""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        response = response.strip()
        try:
            response_int = int(response)
            return response_int
        except ValueError:
            attempt_num+=1
            if attempt_num > num_attempts:
                print(f"error - never returned a single int after {attempt_num} attempts", response)
                return 0
            return self.min_night_stay(num_attempts,attempt_num)
        
    def min_night_stay_complex(self):
        prompt = f"""You are a travel agent, helping me make sense of some hotel documentation.
        Based on the following document, what is the minimum nights stay at a '{self.room_type}' at '{self.property_name}'? If it is dependant on some other factors, describe the rule"""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        return response.strip()

    def cancellation_policy(self, num_attempts=3):
        prompt = f"""In the following document which should contain accommodation information, what is the cancellation policy at a "{self.room_type}" at "{self.property_name}" period?
        Note that the document will likely be using the South African Rand currency. Do not include any currency symbol in your answer.
        If no relevant data is found for "{self.room_type}" at "{self.property_name}" just return 0s.
        Return a YAML response with the following data:
        POLICIES: A list of cancellation policies that each contain the following information:
            START_DAY: The start date of the policy's validity, in relation to the day of the booking. For example, if the policy is "15-10 days before the stay: Pay 25% cancellation fee" the start_day would be 15.
            CANCELLATION_FEE: The fee for cancelling the booking in this date range, as an integer representing the percentage. For example, if the policy is "15-10 days before the stay: Pay 25% cancellation fee" the CANCELLATION_FEE would be 25.
        
        Remember to only return data relating to "{self.room_type}" at "{self.property_name}" and not any other potentially similar accommodations at different properties. If you cannot find figures related to this specific location, rather return zeros.

        """
        return self.llm_handler.sendMessageToLLM_Expecting_Yaml_Response(self.doc,prompt,num_attempts=num_attempts)
    
    def get_stay_type(self, num_attempts=3):
        """
        Get any pay stays/ specials information.

        Returns:
        list: List of the pay stays/ specials information.
            
        """
        prompt = f"""In the following accomodation document, look for any mentions of discounts for staying a certain period, such as "pay stay" offers or stay x days and get a free night .

        Return data that applies to "{self.property_name}" directly, or if the document states the offer applies to "all lodges" or to a group that includes "{self.property_name}", include that as well (unless the other properties are explicitly included or this one is explicitly excluded).

        - **Do NOT** include any pricing, rates, or cost-related details such as STO rates, nightly rates.  
        - Repeated information—each offer and description should be returned **only once**, each in a new line without any special characters or listing formats.

        Return a YAML response with the following data:
        OFFERS: A list of pay stay type offerts that each contain the following information:
            PAY_STAY_DAYS: The number of days that the guest must stay to get the offer. For example, if the offer is "Stay 3, Pay 2" the PAY_STAY_DAYS would be 3. If no relevant data is found, return 0.
            PAY_STAY_FREE_NIGHTS: The amount of nights that are free, e.g with "Stay 3, Pay 2" that is 1 night. If no relevant data is found, return 0.

        If you cannot find figures related to this specific location, **JUST RETURN ZEROS**. """
        
        return self.llm_handler.sendMessageToLLM_Expecting_Yaml_Response(self.doc,prompt,num_attempts=num_attempts)